from fastapi import FastAP<PERSON>, UploadFile, File, Form
from pydantic import BaseModel
from langchain.llms import Ollama
import tempfile
import docx
import fitz
import base64
import requests
import os
import json
import re

app = FastAPI()

class PromptRequest(BaseModel):
    prompt: str
    model: str = "gemma3:4b"  # Default model

# Initialize both models
llm_gemma = Ollama(model="gemma3:4b")
llm_gpt_oss = Ollama(model="gpt-oss:20b")

# Model mapping
MODELS = {
    "gemma3:4b": llm_gemma,
    "gpt-oss:20b": llm_gpt_oss
}

OLLAMA_API_URL = "http://localhost:11434/api/generate"

def extract_text_from_pdf(file_path: str) -> str:
    text = ""
    with fitz.open(file_path) as pdf:
        for page in pdf:
            text += page.get_text()
    return text

def extract_text_from_docx(file_path: str) -> str:
    doc = docx.Document(file_path)
    return "\n".join([para.text for para in doc.paragraphs])

def send_image_to_ollama(prompt: str, image_path: str, model_name: str = "gemma3:4b"):
    try:
        with open(image_path, "rb") as img_file:
            img_data = base64.b64encode(img_file.read()).decode("utf-8")
        response = requests.post(
            OLLAMA_API_URL,
            json={
                "model": model_name,
                "prompt": prompt,
                "images": [img_data],
                "stream": True
            },
            stream=True
        )
        full_response = ""
        for line in response.iter_lines():
            if line:
                chunk = json.loads(line.decode("utf-8"))
                full_response += chunk.get("response", "")
                if chunk.get("done", False):
                    break
        return full_response
    except Exception as e:
        return str(e)


def universal_response(output):
    if isinstance(output, (dict, list)):
        return output
    if not isinstance(output, str):
        return str(output)
    cleaned = re.sub(r'^```json\s*', '', output)
    cleaned = re.sub(r'\s*```$', '', cleaned).strip()
    try:
        parsed = json.loads(cleaned)
        return parsed
    except json.JSONDecodeError:
        return output.strip()


def extract_json_from_response(response):
    if isinstance(response, str):
        cleaned = re.sub(r'^```json\s*', '', response)
        cleaned = re.sub(r'\s*```$', '', cleaned).strip()
        try:
            return json.loads(cleaned)
        except json.JSONDecodeError:
            return response
    return response

def extract_images_from_pdf(file_path: str):
    images_base64 = []
    with fitz.open(file_path) as pdf:
        for page_index in range(len(pdf)):
            page = pdf[page_index]
            image_list = page.get_images(full=True)
            for img_index, img in enumerate(image_list):
                xref = img[0]
                base_image = pdf.extract_image(xref)
                image_bytes = base_image["image"]
                img_base64 = base64.b64encode(image_bytes).decode("utf-8")
                images_base64.append(img_base64)
    return images_base64


@app.post("/generate")
def generate(request: PromptRequest):
    try:
        # Select the appropriate model
        selected_llm = MODELS.get(request.model, llm_gemma)
        response = selected_llm(request.prompt)
        return {"response": response}
    except Exception as e:
        return {"error": str(e)}

@app.post("/generate-from-file")
async def generate_from_file(prompt: str = Form(...), file: UploadFile = File(...), model: str = Form("gemma3:4b")):
    try:
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(await file.read())
            tmp_path = tmp.name
        if file.filename.lower().endswith(".pdf"):
            file_text = extract_text_from_pdf(tmp_path)
            images_base64 = extract_images_from_pdf(tmp_path)
        elif file.filename.lower().endswith(".docx"):
            file_text = extract_text_from_docx(tmp_path)
            images_base64 = []
        else:
            return {"error": "Unsupported file format. Only PDF and DOCX allowed."}
        full_input = f"{prompt}\n\nDocument Content:\n{file_text}"

        # Select the appropriate model
        selected_llm = MODELS.get(model, llm_gemma)
        response = selected_llm(full_input)
        return {
            "response": response,
            "images_base64": images_base64
        }
    except Exception as e:
        return {"error": str(e)}
    finally:
        if 'tmp_path' in locals():
            os.unlink(tmp_path)


@app.post("/generate-from-image")
async def generate_from_image(prompt: str = Form(...), file: UploadFile = File(...), model: str = Form("gemma3:4b")):
    try:
        if not file.filename.lower().endswith((".jpg", ".jpeg", ".png")):
            return {"error": "Unsupported file format. Only JPG, JPEG, PNG allowed."}
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(await file.read())
            tmp_path = tmp.name
        response = send_image_to_ollama(prompt, tmp_path, model)
        parsed_response = universal_response(response)
        return {"response": parsed_response}
    except Exception as e:
        return {"error": str(e)}
    finally:
        if 'tmp_path' in locals():
            os.unlink(tmp_path)

