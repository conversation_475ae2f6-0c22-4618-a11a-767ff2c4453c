from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Form
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import httpx
import asyncio

app = FastAPI(title="Job Post AI Processor UI")

class PromptRequest(BaseModel):
    prompt: str
    model: str = "gemma3:4b"  # Default model

# External API endpoint
EXTERNAL_API_URL = "http://74.99.152.106:40800"

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/api/generate")
async def proxy_generate(request: PromptRequest):
    """Proxy endpoint to handle requests to external API"""
    import time
    start_time = time.time()

    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                f"{EXTERNAL_API_URL}/generate",
                json={"prompt": request.prompt, "model": request.model},
                headers={"Content-Type": "application/json"}
            )

            end_time = time.time()
            response_time = round((end_time - start_time), 2)  # Keep in seconds

            if response.status_code == 200:
                result = response.json()
                # Add response time to the result
                result["response_time_seconds"] = response_time
                return result
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"External API error: {response.text}"
                )
    except httpx.TimeoutException:
        raise HTTPException(status_code=408, detail="Request timeout - API took too long to respond")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="Cannot connect to external API")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Proxy error: {str(e)}")

@app.post("/api/generate-from-file")
async def proxy_generate_from_file(prompt: str = Form(...), file: UploadFile = File(...), model: str = Form("gemma3:4b")):
    """Proxy endpoint to handle file upload requests to external API"""
    import time
    start_time = time.time()

    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            files = {"file": (file.filename, await file.read(), file.content_type)}
            data = {"prompt": prompt, "model": model}

            response = await client.post(
                f"{EXTERNAL_API_URL}/generate-from-file",
                files=files,
                data=data
            )

            end_time = time.time()
            response_time = round((end_time - start_time), 2)

            if response.status_code == 200:
                result = response.json()
                result["response_time_seconds"] = response_time
                return result
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"External API error: {response.text}"
                )
    except httpx.TimeoutException:
        raise HTTPException(status_code=408, detail="Request timeout - API took too long to respond")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="Cannot connect to external API")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Proxy error: {str(e)}")

@app.post("/api/generate-from-image")
async def proxy_generate_from_image(prompt: str = Form(...), file: UploadFile = File(...), model: str = Form("gemma3:4b")):
    """Proxy endpoint to handle image upload requests to external API"""
    import time
    start_time = time.time()

    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            files = {"file": (file.filename, await file.read(), file.content_type)}
            data = {"prompt": prompt, "model": model}

            response = await client.post(
                f"{EXTERNAL_API_URL}/generate-from-image",
                files=files,
                data=data
            )

            end_time = time.time()
            response_time = round((end_time - start_time), 2)

            if response.status_code == 200:
                result = response.json()
                result["response_time_seconds"] = response_time
                return result
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"External API error: {response.text}"
                )
    except httpx.TimeoutException:
        raise HTTPException(status_code=408, detail="Request timeout - API took too long to respond")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="Cannot connect to external API")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Proxy error: {str(e)}")

@app.get("/", response_class=HTMLResponse)
def read_root():
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>jobdesk AI API Testing</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E40AF',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-900">jobdesk AI API Testing</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        API Connected
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Input Section -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                            AI Processor
                        </h3>

                        <!-- API URL Selector -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Select API Model
                            </label>
                            <div class="flex space-x-4">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="modelSelect" value="gemma3:4b" checked
                                           class="form-radio h-4 w-4 text-primary focus:ring-primary border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">Gemma 3:4b</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="modelSelect" value="gpt-oss:20b"
                                           class="form-radio h-4 w-4 text-primary focus:ring-primary border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">GPT-OSS:20b</span>
                                </label>
                            </div>
                        </div>

                        <!-- Processing Type Tabs -->
                        <div class="mb-4">
                            <div class="border-b border-gray-200">
                                <nav class="-mb-px flex space-x-8">
                                    <button onclick="switchTab('text')" id="textTab"
                                            class="tab-button active border-b-2 border-primary text-primary py-2 px-1 text-sm font-medium">
                                        Text Processing
                                    </button>
                                    <button onclick="switchTab('file')" id="fileTab"
                                            class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium">
                                        File Processing
                                    </button>
                                    <button onclick="switchTab('image')" id="imageTab"
                                            class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium">
                                        Image Processing
                                    </button>
                                </nav>
                            </div>
                        </div>



                        <!-- Text Processing Tab Content -->
                        <div id="textContent" class="tab-content">
                            <!-- Prompt Input -->
                            <div class="mb-4">
                                <label for="prompt" class="block text-sm font-medium text-gray-700 mb-2">
                                    AI Prompt
                                </label>
                                <textarea id="prompt" rows="4"
                                        class="shadow-sm focus:ring-primary focus:border-primary mt-1 block w-full sm:text-sm border-gray-300 rounded-md"
                                        placeholder="Enter your prompt here..."></textarea>
                            </div>

                            <!-- Text Content Input -->
                            <div class="mb-6">
                                <label for="textInput" class="block text-sm font-medium text-gray-700 mb-2">
                                    Text Content
                                </label>
                                <textarea id="textInput" rows="8"
                                        class="shadow-sm focus:ring-primary focus:border-primary mt-1 block w-full sm:text-sm border-gray-300 rounded-md"
                                        placeholder="Paste your text content here..."></textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button onclick="processText()"
                                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                                    <svg class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white" id="textLoadingSpinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span id="textButtonText">Process Text</span>
                                </button>
                            </div>
                        </div>

                        <!-- File Processing Tab Content -->
                        <div id="fileContent" class="tab-content hidden">
                            <!-- Prompt Input -->
                            <div class="mb-4">
                                <label for="filePrompt" class="block text-sm font-medium text-gray-700 mb-2">
                                    AI Prompt
                                </label>
                                <textarea id="filePrompt" rows="4"
                                        class="shadow-sm focus:ring-primary focus:border-primary mt-1 block w-full sm:text-sm border-gray-300 rounded-md"
                                        placeholder="Enter your prompt for file processing..."></textarea>
                            </div>

                            <!-- File Upload -->
                            <div class="mb-6">
                                <label for="fileUpload" class="block text-sm font-medium text-gray-700 mb-2">
                                    Upload File (PDF or DOCX)
                                </label>
                                <input type="file" id="fileUpload" accept=".pdf,.docx"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-secondary">
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button onclick="processFile()"
                                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                                    <svg class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white" id="fileLoadingSpinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span id="fileButtonText">Process File</span>
                                </button>
                            </div>
                        </div>

                        <!-- Image Processing Tab Content -->
                        <div id="imageContent" class="tab-content hidden">
                            <!-- Prompt Input -->
                            <div class="mb-4">
                                <label for="imagePrompt" class="block text-sm font-medium text-gray-700 mb-2">
                                    AI Prompt
                                </label>
                                <textarea id="imagePrompt" rows="4"
                                        class="shadow-sm focus:ring-primary focus:border-primary mt-1 block w-full sm:text-sm border-gray-300 rounded-md"
                                        placeholder="Enter your prompt for image analysis..."></textarea>
                            </div>

                            <!-- Image Upload -->
                            <div class="mb-6">
                                <label for="imageUpload" class="block text-sm font-medium text-gray-700 mb-2">
                                    Upload Image (JPG, JPEG, PNG)
                                </label>
                                <input type="file" id="imageUpload" accept=".jpg,.jpeg,.png"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-secondary">
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button onclick="processImage()"
                                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                                    <svg class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white" id="imageLoadingSpinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span id="imageButtonText">Process Image</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Output Section -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex justify-between items-center mb-4">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    AI Response
                                </h3>
                                <div id="responseStats" class="hidden mt-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span id="responseTime">0s</span>
                                    </span>
                                </div>
                            </div>
                            <button onclick="copyResponse()"
                                    class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                Copy
                            </button>
                        </div>
                        
                        <div id="responseContainer" class="min-h-96">
                            <div id="emptyState" class="flex flex-col items-center justify-center h-96 text-gray-500">
                                <svg class="w-12 h-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                                <p class="text-lg font-medium">Ready to process job posts</p>
                                <p class="text-sm">Enter a prompt and job post content to get started</p>
                            </div>
                            
                            <div id="responseContent" class="hidden">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <pre id="responseText" class="whitespace-pre-wrap text-sm text-gray-800"></pre>
                                </div>
                            </div>
                            
                            <div id="errorContent" class="hidden">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                    <div class="flex">
                                        <svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <h4 class="text-red-800 font-medium">Error</h4>
                                            <p id="errorText" class="text-red-700 text-sm mt-1"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // API endpoints (using local proxy)
        const API_URLS = {
            text: '/api/generate',
            file: '/api/generate-from-file',
            image: '/api/generate-from-image'
        };

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active', 'border-primary', 'text-primary');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab content
            document.getElementById(tabName + 'Content').classList.remove('hidden');

            // Add active class to selected tab
            const activeTab = document.getElementById(tabName + 'Tab');
            activeTab.classList.add('active', 'border-primary', 'text-primary');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
        }

        async function processText() {
            const prompt = document.getElementById('prompt').value.trim();
            const textInput = document.getElementById('textInput').value.trim();
            const selectedModel = document.querySelector('input[name="modelSelect"]:checked').value;

            if (!prompt || !textInput) {
                showError('Please enter both a prompt and text content.');
                return;
            }

            // Show loading state
            setLoading('text', true);
            hideAllStates();

            const fullPrompt = `${prompt}\\n\\n${textInput}`;

            try {
                const response = await fetch(API_URLS.text, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: fullPrompt,
                        model: selectedModel
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    // Handle different response formats
                    let responseContent = data.response || data.result || data;
                    const responseTime = data.response_time_seconds || 0;
                    showResponse(responseContent, responseTime);
                } else {
                    showError(data.error || data.message || 'An error occurred while processing the request.');
                }
            } catch (error) {
                showError(`Failed to connect to the API: ${error.message}`);
            } finally {
                setLoading('text', false);
            }
        }

        async function processFile() {
            const prompt = document.getElementById('filePrompt').value.trim();
            const fileInput = document.getElementById('fileUpload');
            const selectedModel = document.querySelector('input[name="modelSelect"]:checked').value;

            if (!prompt || !fileInput.files[0]) {
                showError('Please enter a prompt and select a file.');
                return;
            }

            // Show loading state
            setLoading('file', true);
            hideAllStates();

            const formData = new FormData();
            formData.append('prompt', prompt);
            formData.append('file', fileInput.files[0]);
            formData.append('model', selectedModel);

            try {
                const response = await fetch(API_URLS.file, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    // Handle different response formats
                    let responseContent = data.response || data.result || data;
                    const responseTime = data.response_time_seconds || 0;
                    showResponse(responseContent, responseTime);
                } else {
                    showError(data.error || data.message || 'An error occurred while processing the file.');
                }
            } catch (error) {
                showError(`Failed to connect to the API: ${error.message}`);
            } finally {
                setLoading('file', false);
            }
        }

        async function processImage() {
            const prompt = document.getElementById('imagePrompt').value.trim();
            const imageInput = document.getElementById('imageUpload');
            const selectedModel = document.querySelector('input[name="modelSelect"]:checked').value;

            if (!prompt || !imageInput.files[0]) {
                showError('Please enter a prompt and select an image.');
                return;
            }

            // Show loading state
            setLoading('image', true);
            hideAllStates();

            const formData = new FormData();
            formData.append('prompt', prompt);
            formData.append('file', imageInput.files[0]);
            formData.append('model', selectedModel);

            try {
                const response = await fetch(API_URLS.image, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    // Handle different response formats
                    let responseContent = data.response || data.result || data;
                    const responseTime = data.response_time_seconds || 0;
                    showResponse(responseContent, responseTime);
                } else {
                    showError(data.error || data.message || 'An error occurred while processing the image.');
                }
            } catch (error) {
                showError(`Failed to connect to the API: ${error.message}`);
            } finally {
                setLoading('image', false);
            }
        }

        function setLoading(type, loading) {
            const button = document.querySelector(`button[onclick="process${type.charAt(0).toUpperCase() + type.slice(1)}()"]`);
            const spinner = document.getElementById(`${type}LoadingSpinner`);
            const buttonText = document.getElementById(`${type}ButtonText`);

            if (loading) {
                button.disabled = true;
                button.classList.add('opacity-75', 'cursor-not-allowed');
                spinner.classList.remove('hidden');
                buttonText.textContent = 'Processing...';
            } else {
                button.disabled = false;
                button.classList.remove('opacity-75', 'cursor-not-allowed');
                spinner.classList.add('hidden');
                const buttonTexts = {
                    'text': 'Process Text',
                    'file': 'Process File',
                    'image': 'Process Image'
                };
                buttonText.textContent = buttonTexts[type] || 'Process';
            }
        }

        function hideAllStates() {
            document.getElementById('emptyState').classList.add('hidden');
            document.getElementById('responseContent').classList.add('hidden');
            document.getElementById('errorContent').classList.add('hidden');
            document.getElementById('responseStats').classList.add('hidden');
        }

        function showResponse(response, responseTime = 0) {
            hideAllStates();

            // Handle different types of responses
            let displayText = response;

            // If response is an object or array, format it as JSON
            if (typeof response === 'object') {
                displayText = JSON.stringify(response, null, 2);
            } else if (typeof response === 'string') {
                // Try to parse as JSON and reformat if valid
                try {
                    const parsed = JSON.parse(response);
                    displayText = JSON.stringify(parsed, null, 2);
                } catch (e) {
                    // If not valid JSON, keep as is
                    displayText = response;
                }
            }

            document.getElementById('responseText').textContent = displayText;
            document.getElementById('responseContent').classList.remove('hidden');

            // Show response time
            if (responseTime > 0) {
                document.getElementById('responseTime').textContent = `${responseTime}s`;
                document.getElementById('responseStats').classList.remove('hidden');
            }
        }

        function showError(error) {
            hideAllStates();
            document.getElementById('errorText').textContent = error;
            document.getElementById('errorContent').classList.remove('hidden');
        }

        function copyResponse() {
            const responseText = document.getElementById('responseText').textContent;
            if (responseText) {
                navigator.clipboard.writeText(responseText).then(() => {
                    // Show brief success feedback
                    const button = event.target.closest('button');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Copied!';
                    setTimeout(() => {
                        button.innerHTML = originalText;
                    }, 2000);
                });
            }
        }


    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3001)
