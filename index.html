<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Post AI Processor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E40AF',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-900">Job Post AI Processor</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        API Ready
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Input Section -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                            Process Job Post
                        </h3>
                        
                        <!-- Prompt Templates -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Quick Templates
                            </label>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                <button onclick="setTemplate('skills')" 
                                        class="px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 hover:bg-gray-100 transition-colors">
                                    Extract Skills
                                </button>
                                <button onclick="setTemplate('requirements')" 
                                        class="px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 hover:bg-gray-100 transition-colors">
                                    Analyze Requirements
                                </button>
                                <button onclick="setTemplate('summary')" 
                                        class="px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 hover:bg-gray-100 transition-colors">
                                    Summarize Job
                                </button>
                                <button onclick="setTemplate('custom')" 
                                        class="px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 hover:bg-gray-100 transition-colors">
                                    Custom Prompt
                                </button>
                            </div>
                        </div>

                        <!-- Prompt Input -->
                        <div class="mb-4">
                            <label for="prompt" class="block text-sm font-medium text-gray-700 mb-2">
                                AI Prompt
                            </label>
                            <textarea id="prompt" rows="4" 
                                    class="shadow-sm focus:ring-primary focus:border-primary mt-1 block w-full sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Enter your prompt here..."></textarea>
                        </div>

                        <!-- Job Post Input -->
                        <div class="mb-6">
                            <label for="jobPost" class="block text-sm font-medium text-gray-700 mb-2">
                                Job Post Content
                            </label>
                            <textarea id="jobPost" rows="8" 
                                    class="shadow-sm focus:ring-primary focus:border-primary mt-1 block w-full sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Paste the job posting here..."></textarea>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <button onclick="processJobPost()" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                                <svg class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white" id="loadingSpinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span id="buttonText">Process Job Post</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Output Section -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                AI Response
                            </h3>
                            <button onclick="copyResponse()" 
                                    class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                Copy
                            </button>
                        </div>
                        
                        <div id="responseContainer" class="min-h-96">
                            <div id="emptyState" class="flex flex-col items-center justify-center h-96 text-gray-500">
                                <svg class="w-12 h-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                                <p class="text-lg font-medium">Ready to process job posts</p>
                                <p class="text-sm">Enter a prompt and job post content to get started</p>
                            </div>
                            
                            <div id="responseContent" class="hidden">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <pre id="responseText" class="whitespace-pre-wrap text-sm text-gray-800 font-mono"></pre>
                                </div>
                            </div>
                            
                            <div id="errorContent" class="hidden">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                    <div class="flex">
                                        <svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <h4 class="text-red-800 font-medium">Error</h4>
                                            <p id="errorText" class="text-red-700 text-sm mt-1"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Template prompts
        const templates = {
            skills: "Extract all required skills from this job description and return them as a JSON array of strings:",
            requirements: "Analyze this job posting and provide:\n1. Required skills\n2. Experience level needed\n3. Education requirements\n4. Key responsibilities\n\nJob Description:",
            summary: "Summarize this job posting in 3-4 bullet points highlighting the most important aspects:\n\nJob Description:",
            custom: ""
        };

        function setTemplate(type) {
            document.getElementById('prompt').value = templates[type];
        }

        async function processJobPost() {
            const prompt = document.getElementById('prompt').value.trim();
            const jobPost = document.getElementById('jobPost').value.trim();
            
            if (!prompt || !jobPost) {
                showError('Please enter both a prompt and job post content.');
                return;
            }

            // Show loading state
            setLoading(true);
            hideAllStates();

            const fullPrompt = `${prompt}\n\n${jobPost}`;

            try {
                const response = await fetch('http://localhost:8000/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: fullPrompt
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showResponse(data.response);
                } else {
                    showError(data.error || 'An error occurred while processing the request.');
                }
            } catch (error) {
                showError('Failed to connect to the API. Make sure the server is running on http://localhost:8000');
            } finally {
                setLoading(false);
            }
        }

        function setLoading(loading) {
            const button = document.querySelector('button[onclick="processJobPost()"]');
            const spinner = document.getElementById('loadingSpinner');
            const buttonText = document.getElementById('buttonText');
            
            if (loading) {
                button.disabled = true;
                button.classList.add('opacity-75', 'cursor-not-allowed');
                spinner.classList.remove('hidden');
                buttonText.textContent = 'Processing...';
            } else {
                button.disabled = false;
                button.classList.remove('opacity-75', 'cursor-not-allowed');
                spinner.classList.add('hidden');
                buttonText.textContent = 'Process Job Post';
            }
        }

        function hideAllStates() {
            document.getElementById('emptyState').classList.add('hidden');
            document.getElementById('responseContent').classList.add('hidden');
            document.getElementById('errorContent').classList.add('hidden');
        }

        function showResponse(response) {
            hideAllStates();
            document.getElementById('responseText').textContent = response;
            document.getElementById('responseContent').classList.remove('hidden');
        }

        function showError(error) {
            hideAllStates();
            document.getElementById('errorText').textContent = error;
            document.getElementById('errorContent').classList.remove('hidden');
        }

        function copyResponse() {
            const responseText = document.getElementById('responseText').textContent;
            if (responseText) {
                navigator.clipboard.writeText(responseText).then(() => {
                    // Show brief success feedback
                    const button = event.target.closest('button');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Copied!';
                    setTimeout(() => {
                        button.innerHTML = originalText;
                    }, 2000);
                });
            }
        }

        // Set default template
        setTemplate('skills');
    </script>
</body>
</html>
